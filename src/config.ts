// tslint:disable:max-line-length
import { HttpGatewayConfig } from "@skywind-group/sw-integration-core";

const http: HttpGatewayConfig = {
    operatorUrl: process.env.OPERATOR_HTTP_URL || "http://operator_url/",
    defaultOptions: {
        timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 10000,
        proxy: process.env.OPERATOR_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 60000,
        freeSocketKeepAliveTimeout: +process.env.OPERTOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 30000
    },
    ssl: {
        ca: process.env.OPERATOR_SSL_CA,
        key: process.env.OPERATOR_SSL_KEY,
        cert: process.env.OPERATOR_SSL_CERT,
        password: process.env.OPERATOR_SSL_PASSWORD
    }
};

const config = {
    environment: process.env.NODE_ENV || "development",

    isProduction: (): boolean => {
        return config.environment === "production";
    },

    region: process.env.REGION_TYPE || "default",

    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 6000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 6001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 6002,
        mockPort: +process.env.SERVER_MOCK_PORT || 6003
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4054,
        api: {
            isEnabled: process.env.INTERNAL_API === "true"
        }
    },

    logLevel: process.env.LOG_LEVEL || "info",

    loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || (process.env.GRAYLOG_HOST && "graylog") || "console") as any,

    http,

    merchantType: process.env.MERCH_TYPE || "hub88",
    operatorAPIBaseUrl: process.env.OPERATOR_API_BASE_URL || "http://localhost:3000",
    internalMAPIUrl: process.env.INTERNAL_MAPI_URL || "http://localhost:4004",
    operator: {
        cryptoAlgorithm: process.env.OPERATOR_CRYPTO_ALG || "RSA-SHA256",
        retryPolicy: {
            sleepInterval: +(process.env.RETRIES_SLEEP_TIMEOUT || 500), // in ms (default 0.5 sec)
            attempts: +process.env.RETRIES_ATTEMPTS || 3 // in ms (default 3 times)
        }
    },
    roundImageHistoryTTL: +process.env.ROUND_IMAGE_HISTORY_TTL || 600, // 10 min (in seconds)
    gameImagesServer: process.env.GAME_IMAGES_SERVER || "https://static-de-gcpbucket.sw420101.com/customers/Hub88",
    lobbyGameCode: process.env.LOBBY_GAME_CODE || undefined
};

export default config;
