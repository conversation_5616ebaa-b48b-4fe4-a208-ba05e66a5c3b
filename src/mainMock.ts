/**
 * Should be the first Lines
 */
import { bootstrapMock } from "@skywind-group/sw-integration-core";

import config from "./config";
import { AdapterMockModule } from "./mock/mock.module";
import { NestFastifyApplication } from "@nestjs/platform-fastify";

async function bootstrap() {
    const app: NestFastifyApplication = (await bootstrapMock({
        serviceName: "sw-integration-hub88-mock",
        versionFile: "./lib/version",
        module: AdapterMockModule,
        internalPort: config.internalServer.port,
        port: config.server.mockPort,
        actions: ["getbalance", "credit", "debit", "rollback"],
        doNotStartApplication: true
    })) as NestFastifyApplication;

    await app.register((fastify, options, done) => done());

    await app.listen(config.server.mockPort, "0.0.0.0");
}
void bootstrap();
