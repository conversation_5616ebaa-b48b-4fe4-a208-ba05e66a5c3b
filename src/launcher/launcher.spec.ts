import { LauncherService } from "./launcher.service";
import { BaseHttpService, InternalAPIService, SkywindGameTypes } from "@skywind-group/sw-wallet-adapter-core";
import { Hub88GameTypes, Platform } from "../common/entities/hub88.entities";
import { countries } from "@skywind-group/sw-integration-core";
import config from "../config";

describe("Launcher", () => {
    const url = "http://test.url.com";
    let launcherService: LauncherService;
    let baseHttpService: BaseHttpService;
    let internalApiService: InternalAPIService;

    beforeEach(() => {
        baseHttpService = new BaseHttpService("http://lol.com");
        internalApiService = new InternalAPIService("lol");
        launcherService = new LauncherService(baseHttpService, internalApiService);
    });

    it("getGameUrl", async () => {
        jest.spyOn(baseHttpService, "post").mockImplementation(async () => {
            return { url };
        });
        expect(
            await launcherService.getUrl({
                currency: "USD",
                game_code: "sw_gol",
                lang: "en",
                ip: "***********",
                user: "playerCode",
                token: "token",
                platform: Platform.DESKTOP,
                country: "RU",
                operator_id: "hub88",
                lobby_url: "lobby.url"
            })
        ).toStrictEqual({ url });
    });

    it("getHistoryImage", async () => {
        jest.spyOn(internalApiService, "post").mockImplementation(async () => {
            return { imageUrl: url };
        });

        expect(
            await launcherService.getRoundHistoryImage({
                operator_id: "1",
                round: "123"
            })
        ).toStrictEqual({ url });
    });

    it("game list", async () => {
        const gameCode = "test";
        jest.spyOn(internalApiService, "getMerchantEntityInfo").mockImplementation(async () => {
            return { countries: countries } as any;
        });
        jest.spyOn(internalApiService, "getGames").mockImplementation(async () => {
            return [
                {
                    code: gameCode,
                    title: "Test game",
                    type: SkywindGameTypes.Slot,
                    status: "normal"
                }
            ] as any;
        });
        expect(
            await launcherService.getGames({
                operator_id: "1"
            })
        ).toStrictEqual([
            {
                url_thumb: `${config.gameImagesServer}/${gameCode}/${gameCode}-thumbnail.png`,
                url_background: `${config.gameImagesServer}/${gameCode}/${gameCode}-bg.png`,
                platforms: [Platform.DESKTOP, Platform.MOBILE],
                product: "SkyWind",
                game_code: "test",
                name: "Test game",
                enabled: true,
                freebet_support: false,
                category: Hub88GameTypes.Video_Slots,
                blocked_countries: []
            }
        ]);
    });
});
