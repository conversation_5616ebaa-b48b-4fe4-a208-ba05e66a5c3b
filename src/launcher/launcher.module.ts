import { <PERSON>du<PERSON> } from "@nestjs/common";
import { LauncherController } from "./launcher.controller";
import { LauncherService } from "./launcher.service";
import { BaseHttpService, InternalAPIService } from "@skywind-group/sw-wallet-adapter-core";
import config from "../config";
import { Names } from "../names";
import { APP_FILTER, BaseExceptionFilter } from "@nestjs/core";

@Module({
    controllers: [LauncherController],
    providers: [
        LauncherService,
        { useValue: new BaseHttpService(config.operatorAPIBaseUrl), provide: Names.BaseHttpService },
        { useValue: new InternalAPIService(config.internalMAPIUrl), provide: Names.InternalAPIService },
        {
            provide: APP_FILTER,
            useClass: BaseExceptionFilter
        }
    ]
})
export class LauncherModule {}
