import { Body, Controller, HttpCode, Post, UseFilters, UsePipes, ValidationPipe } from "@nestjs/common";
import { BaseErrorsFilter } from "../common/exceptionFilter";
import { LauncherService } from "./launcher.service";
import {
    GameListRequest,
    GameListResponse,
    GameUrlRequest,
    HistoryRequest,
    LaunchObject
} from "../common/entities/hub88.entities";

@Controller("/game")
@UseFilters(BaseErrorsFilter)
export class LauncherController {
    constructor(private launcherService: LauncherService) {}

    @Post("/url")
    @HttpCode(200)
    @UsePipes(new ValidationPipe({ transform: true }))
    async getUrl(@Body() getUrlRequest: GameUrlRequest): Promise<LaunchObject> {
        return await this.launcherService.getUrl(getUrlRequest);
    }

    @Post("/round")
    @HttpCode(200)
    @UsePipes(new ValidationPipe({ transform: true }))
    async getGameRoundHistoryImage(@Body() getRoundHistoryRequest: HistoryRequest): Promise<{ url: string }> {
        return await this.launcherService.getRoundHistoryImage(getRoundHistoryRequest);
    }

    @Post("/list")
    @UsePipes(new ValidationPipe({ transform: true }))
    @HttpCode(200)
    async getGames(@Body() getGamesRequest: GameListRequest): Promise<GameListResponse[]> {
        return await this.launcherService.getGames(getGamesRequest);
    }
}
