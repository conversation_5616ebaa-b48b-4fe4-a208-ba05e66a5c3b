import { Inject, Injectable } from "@nestjs/common";
import { Names } from "../names";
import {
    BaseHttpService,
    generateInternalToken,
    InternalAPIService,
    PlayMode,
    SkywindGameTypes
} from "@skywind-group/sw-wallet-adapter-core";
import {
    GameListRequest,
    GameListResponse,
    GameUrlRequest,
    HistoryRequest,
    Hub88GameTypes,
    LaunchObject,
    Platform,
    SWGameLaunchResponse
} from "../common/entities/hub88.entities";
import {
    IntegrationMerchantGameInitRequest,
    MerchantGameHistoryRequest,
    MerchantGameHistoryResponse,
    MerchantLobbyLaunchRequest
} from "../common/entities/skywind.entities";
import config from "../config";
import { countries } from "@skywind-group/sw-integration-core";

@Injectable()
export class LauncherService {
    constructor(
        @Inject(Names.BaseHttpService) private httpService: BaseHttpService,
        @Inject(Names.InternalAPIService) private internalApiService: InternalAPIService
    ) {
    }

    public async getUrl(gameUrlRequest: GameUrlRequest): Promise<LaunchObject> {
        const playMode = gameUrlRequest.currency === "XXX" ? PlayMode.FUN : PlayMode.REAL;
        const data: IntegrationMerchantGameInitRequest = {
            merchantType: gameUrlRequest.merchantType || config.merchantType,
            merchantCode: gameUrlRequest.operator_id,
            gameCode: gameUrlRequest.game_code,
            playMode: playMode,
            playmode: playMode,
            language: gameUrlRequest.lang,
            ip: gameUrlRequest.ip,
            cashier: gameUrlRequest.deposit_url,
            lobby: gameUrlRequest.lobby_url,
            country: gameUrlRequest.country,
            playerCode: gameUrlRequest.user,
            currency: gameUrlRequest.currency === "XXX" ? "EUR" : gameUrlRequest.currency,
            platform: gameUrlRequest.platform,
            token: gameUrlRequest.token,
            subPartnerId: gameUrlRequest.sub_partner_id
        };
        let uri: string;
        let payload: IntegrationMerchantGameInitRequest | MerchantLobbyLaunchRequest;
        if (config.lobbyGameCode && config.lobbyGameCode === data.gameCode) {
            uri = "v1/merchants/lobby/url";
            payload = {
                merchantType: data.merchantType,
                merchantCode: data.merchantCode,
                language: data.language,
                ticket: await generateInternalToken<IntegrationMerchantGameInitRequest>(data)
            };
        } else {
            uri = "v1/merchants/game/url";
            payload = data;

        }
        const { url } = await this.httpService.post<SWGameLaunchResponse>(uri, payload);
        return { url };
    }

    public async getRoundHistoryImage(getHistoryRequest: HistoryRequest): Promise<{ url: string }> {
        const apiRequest: MerchantGameHistoryRequest = {
            merchantType: getHistoryRequest.merchantType || config.merchantType,
            merchantCode: getHistoryRequest.operator_id,
            roundId: getHistoryRequest.round,
            ttl: config.roundImageHistoryTTL,
            finishDate: new Date().toISOString(),
            showRoundInfo: true
        };

        const options = {
            auth: {
                internalToken: await generateInternalToken({})
            }
        };
        const historyVisualisation = await this.internalApiService.post<MerchantGameHistoryResponse>(
            "v1/merchants/history/image",
            apiRequest,
            options
        );

        return { url: historyVisualisation.imageUrl };
    }

    public async getGames(getGamesRequest: GameListRequest): Promise<GameListResponse[]> {
        const mrchType = getGamesRequest.merchantType || config.merchantType;
        const merchInfo = await this.internalApiService.getMerchantEntityInfo(getGamesRequest.operator_id, mrchType);
        const blockedCountries = countries.filter((country) => !merchInfo.countries.includes(country));
        const merchantGames = await this.internalApiService.getGames(getGamesRequest.operator_id, mrchType);
        return merchantGames.map((merchGame) => {
            return {
                url_thumb: `${config.gameImagesServer}/${merchGame.code}/${merchGame.code}-thumbnail.png`,
                url_background: `${config.gameImagesServer}/${merchGame.code}/${merchGame.code}-bg.png`,
                platforms: [Platform.DESKTOP, Platform.MOBILE],
                product: "SkyWind",
                game_code: merchGame.code,
                name: merchGame.title,
                enabled: merchGame.status !== "suspended",
                freebet_support: false,
                category: this.getGameType(merchGame.type),
                blocked_countries: blockedCountries
            };
        });
    }

    private getGameType(swType: SkywindGameTypes) {
        switch (swType) {
            case SkywindGameTypes.External:
                return Hub88GameTypes.Unknown;
            case SkywindGameTypes.Action:
                return Hub88GameTypes.Jackpot_Slots;
            case SkywindGameTypes.Slot:
                return Hub88GameTypes.Video_Slots;
            case SkywindGameTypes.Table:
                return Hub88GameTypes.Table_Games;
            default:
                return Hub88GameTypes.Unknown;
        }
    }
}
