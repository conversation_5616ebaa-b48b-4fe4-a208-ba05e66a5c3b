import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { errors } from "@skywind-group/sw-utils";
import ERROR_LEVEL = errors.ERROR_LEVEL;

export class AdapterError extends SWError {
    constructor(status: number, code: number, message: string) {
        super(status || 500, code, message);
    }
}

export class ValidationError extends SWError {
    constructor(message: string) {
        super(400, 40, message, ERROR_LEVEL.WARN, {}, { messages: [message] });
    }
}

export class UnknowError extends SWError {
    constructor(message: string) {
        super(400, 807, message);
    }
}

export class RequireRefundBetError extends SWError {
    constructor(message = "Require to refund bet") {
        super(400, 800, message);
    }
}

export class OperatorError extends SWError {
    constructor(status: number, message: string) {
        super(status, undefined, message);
    }
}
