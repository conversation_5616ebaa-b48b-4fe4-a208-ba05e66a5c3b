import { Platform } from "../../common/entities/hub88.entities";
import { AuthorizeHandler } from "./authorize.handler";
import {
    IntegrationMerchantGameInitRequest,
    IntegrationStartGameTokenData
} from "../../common/entities/skywind.entities";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { CreateGameUrlRequest } from "@skywind-group/sw-integration-core";
import { privateKey } from "../balance/balance.spec";

const GAME_CODE = "sw_al";
const merchantInfo = {
    type: "hub88",
    code: "hub88",
    brandId: 365,
    params: {
        serverUrl: "",
        privateKey
    }
};
const startGameTokenData: IntegrationStartGameTokenData = {
    brandId: merchantInfo.brandId,
    merchantType: merchantInfo.type,
    merchantCode: merchantInfo.code,
    playerCode: "pl_123",
    gameGroup: undefined,
    gameCode: GAME_CODE,
    providerCode: "sw_al_provider",
    providerGameCode: "sw_al_game_provider",
    currency: "USD",
    country: "BY",
    language: "en",
    playmode: PlayMode.REAL,
    platform: Platform.DESKTOP,
    token: "token"
};
const newGameCode = `new_${GAME_CODE}`;
const request: CreateGameUrlRequest<IntegrationMerchantGameInitRequest> = {
    gameCode: newGameCode,
    providerCode: startGameTokenData.providerCode,
    providerGameCode: startGameTokenData.providerGameCode,
    merchantInfo,
    initRequest: {
        playerCode: startGameTokenData.playerCode,
        merchantType: merchantInfo.type,
        merchantCode: merchantInfo.code,
        gameCode: startGameTokenData.gameCode,
        currency: startGameTokenData.currency,
        country: startGameTokenData.country,
        language: startGameTokenData.language,
        playmode: startGameTokenData.playmode,
        playMode: startGameTokenData.playmode,
        platform: Platform.DESKTOP,
        token: startGameTokenData.token,
        lobby: "www.locallobby.net"
    }
};

describe("AuthorizeHandler", () => {
    const handler = new AuthorizeHandler();

    it("build", async () => {
        const result = await handler.build(request);
        expect(result.payload.game_code).toEqual(GAME_CODE);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });
});
