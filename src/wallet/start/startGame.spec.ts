import { StartGameService } from "./startGame.service";
import { CreateGameTokenRequest, CreateGameUrlRequest, HttpGateway } from "@skywind-group/sw-integration-core";
import {
    IntegrationMerchantGameInitRequest,
    IntegrationStartGameTokenData
} from "../../common/entities/skywind.entities";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { Platform } from "../../common/entities/hub88.entities";
import config from "../../config";

describe("startGame", () => {
    const GAME_CODE = "sw_al";
    const merchantInfo = {
        type: "hub88",
        code: "hub88",
        brandId: 365,
        params: {
            username: "siroga2",
            password: "666",
            serverUrl: "serverUrl"
        }
    };
    const tokenRequest: CreateGameTokenRequest<IntegrationStartGameTokenData> = {
        gameCode: GAME_CODE,
        merchantInfo: merchantInfo,
        startGameToken: {
            providerCode: "sw_al_provider",
            providerGameCode: "sw_al_game_provider",
            token: "session token",
            playerCode: "test_player",
            gameCode: GAME_CODE,
            currency: "USD",
            merchantType: "hub88",
            merchantCode: "hub88",
            brandId: 1111,
            platform: Platform.DESKTOP,
            playmode: PlayMode.REAL
        },
        currency: "USD",
        transferEnabled: true
    };
    const httpGateway = new HttpGateway(config.http);
    let startGameService: StartGameService;

    beforeEach(() => {
        startGameService = new StartGameService();
    });

    it("createGameUrl", async () => {
        jest.spyOn(httpGateway, "request").mockImplementation(async () => {
            return { token: "token_b" };
        });
        const request: CreateGameUrlRequest<IntegrationMerchantGameInitRequest> = {
            gameCode: GAME_CODE,
            providerCode: "sw_al_provider",
            providerGameCode: "sw_al_game_provider",
            merchantInfo,
            initRequest: {
                playerCode: "pl_123",
                currency: "USD",
                merchantType: "hub88",
                merchantCode: "hub88",
                gameCode: GAME_CODE,
                language: "en",
                playmode: PlayMode.REAL,
                playMode: PlayMode.REAL,
                lobby: "www.locallobby.net",
                platform: Platform.DESKTOP,
                country: "BY",
                cashier: undefined,
                token: "token_a"
            }
        };
        const result = await startGameService.createGameUrl(request);
        expect(result.urlParams).toStrictEqual({
            language: "en",
            lobby: "www.locallobby.net",
            cashier: undefined
        });
        expect(result.tokenData).toStrictEqual({
            brandId: merchantInfo.brandId,
            merchantType: merchantInfo.type,
            merchantCode: merchantInfo.code,
            playerCode: request.initRequest.playerCode,
            gameGroup: undefined,
            gameCode: request.gameCode,
            providerGameCode: request.providerGameCode,
            providerCode: request.providerCode,
            currency: request.initRequest.currency,
            country: request.initRequest.country,
            language: request.initRequest.language,
            playmode: request.initRequest.playmode,
            platform: request.initRequest.platform,
            token: "token_a"
        });
    });

    it("createGameTokenData", async () => {
        const result = await startGameService.createGameTokenData(tokenRequest);
        expect(result.gameTokenData).toStrictEqual({
            playerCode: tokenRequest.startGameToken.playerCode,
            gameCode: tokenRequest.startGameToken.gameCode,
            brandId: tokenRequest.startGameToken.brandId,
            currency: tokenRequest.startGameToken.currency,
            merchantType: tokenRequest.merchantInfo.type,
            merchantCode: tokenRequest.merchantInfo.code,
            test: tokenRequest.startGameToken.test,
            transferEnabled: tokenRequest.transferEnabled,
            isPromoInternal: false,
            playmode: tokenRequest.startGameToken.playmode,
            envId: undefined,
            token: tokenRequest.startGameToken.token
        });
    });

    it("launchNewGame", async () => {
        const newToken = "new_token";
        jest.spyOn(httpGateway, "request").mockImplementation(async () => {
            return Promise.resolve({ token: newToken });
        });

        const startGameTokenData: IntegrationStartGameTokenData = {
            brandId: merchantInfo.brandId,
            merchantType: merchantInfo.type,
            merchantCode: merchantInfo.code,
            playerCode: "pl_123",
            gameGroup: undefined,
            gameCode: GAME_CODE,
            providerCode: "sw_al_provider",
            providerGameCode: "sw_al_game_provider",
            currency: "USD",
            country: "BY",
            language: "en",
            playmode: PlayMode.REAL,
            platform: Platform.DESKTOP,
            token: "token"
        };
        const newGameCode = `new_${GAME_CODE}`;
        const request: CreateGameUrlRequest<IntegrationMerchantGameInitRequest> = {
            gameCode: newGameCode,
            providerCode: startGameTokenData.providerCode,
            providerGameCode: startGameTokenData.providerGameCode,
            merchantInfo,
            initRequest: {
                playerCode: startGameTokenData.playerCode,
                merchantType: merchantInfo.type,
                merchantCode: merchantInfo.code,
                gameCode: startGameTokenData.gameCode,
                currency: startGameTokenData.currency,
                country: startGameTokenData.country,
                language: startGameTokenData.language,
                playmode: startGameTokenData.playmode,
                playMode: startGameTokenData.playmode,
                platform: Platform.DESKTOP,
                token: startGameTokenData.token,
                lobby: "www.locallobby.net",
                cashier: undefined,
                previousStartTokenData: startGameTokenData
            }
        };

        const result = await startGameService.createGameUrl(request);
        expect(result.tokenData).toStrictEqual({ ...startGameTokenData, gameCode: newGameCode });
    });
});
