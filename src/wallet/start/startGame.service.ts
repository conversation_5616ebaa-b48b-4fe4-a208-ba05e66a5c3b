import {
    CreateGameTokenRequest,
    CreateGameTokenSupport,
    CreateGameUrlRequest,
    CreateGameUrlSupport
} from "@skywind-group/sw-integration-core";
import {
    LoginTerminalRequest,
    LoginTerminalResponse,
    MerchantGameTokenInfo,
    MerchantInfo,
    parseInternalToken
} from "@skywind-group/sw-wallet-adapter-core";
import { Injectable } from "@nestjs/common";
import {
    IntegrationGameTokenData,
    IntegrationMerchantGameInitRequest,
    IntegrationMerchantGameURLInfo,
    IntegrationStartGameTokenData
} from "../../common/entities/skywind.entities";
import config from "../../config";
import { AdapterError } from "../../error";

type UrlRequest = CreateGameUrlRequest<IntegrationMerchantGameInitRequest>;

@Injectable()
export class StartGameService
    implements CreateGameUrlSupport<IntegrationMerchantGameInitRequest>,
        CreateGameTokenSupport<IntegrationStartGameTokenData, IntegrationGameTokenData> {

    public async createGameUrl(req: UrlRequest): Promise<IntegrationMerchantGameURLInfo> {
        return {
            tokenData: {
                brandId: req.merchantInfo.brandId,
                merchantType: req.merchantInfo.type,
                merchantCode: req.merchantInfo.code,
                playerCode: req.initRequest.playerCode,
                gameGroup: undefined,
                gameCode: req.gameCode,
                providerGameCode: req.providerGameCode,
                providerCode: req.providerCode,
                currency: req.initRequest.currency,
                country: req.initRequest.country,
                language: req.initRequest.language,
                playmode: req.initRequest.playmode,
                platform: req.initRequest.platform,
                token: req.initRequest.token
            },
            // all params are optional - will be appended to game url if present
            urlParams: {
                language: req.initRequest.language,
                lobby: req.initRequest.lobby || undefined,
                cashier: req.initRequest.cashier || undefined
            }
        };
    }

    public async createGameTokenData(
        req: CreateGameTokenRequest<IntegrationStartGameTokenData>
    ): Promise<MerchantGameTokenInfo<IntegrationGameTokenData>> {
        return {
            gameTokenData: {
                playerCode: req.startGameToken.playerCode,
                gameCode: req.startGameToken.gameCode,
                brandId: req.startGameToken.brandId,
                currency: req.startGameToken.currency,
                merchantType: req.merchantInfo.type,
                merchantCode: req.merchantInfo.code,
                test: req.startGameToken.test,
                transferEnabled: req.transferEnabled,
                isPromoInternal: req.merchantInfo.params.isPromoInternal || false,
                playmode: req.startGameToken.playmode,
                envId: req.startGameToken.envId,
                token: req.startGameToken.token
            }
        };
    }

    public async loginTerminalPlayer(
        merchantInfo: MerchantInfo,
        { ticket }: LoginTerminalRequest
    ): Promise<LoginTerminalResponse<IntegrationStartGameTokenData>> {
        const gameCode = config.lobbyGameCode;
        if (gameCode) {
            const payload: IntegrationMerchantGameInitRequest = await parseInternalToken(ticket);
            const tokenData = {
                brandId: merchantInfo.brandId,
                merchantType: merchantInfo.type,
                merchantCode: merchantInfo.code,
                playerCode: payload.playerCode,
                gameGroup: undefined,
                gameCode: undefined,
                providerGameCode: undefined,
                providerCode: undefined,
                currency: payload.currency,
                country: payload.country,
                language: payload.language,
                playmode: payload.playmode,
                platform: payload.platform,
                token: payload.token
            };
            return {
                tokenData,
                sessionId: tokenData.token
            };
        }
        throw new AdapterError(500, 506, "loginTerminalPlayer is not supported");
    }
}
