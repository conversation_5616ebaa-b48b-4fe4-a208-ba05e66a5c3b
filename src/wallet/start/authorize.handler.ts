import { CreateGameUrlRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { BaseHandler } from "../base.handler";
import { IntegrationMerchantGameInitRequest } from "../../common/entities/skywind.entities";
import * as superagent from "superagent";
import { Injectable } from "@nestjs/common";
import { AuthorizeResponse } from "../../common/entities/hub88.entities";

type HandlerRequest = CreateGameUrlRequest<IntegrationMerchantGameInitRequest>;

@Injectable()
export class AuthorizeHandler extends BaseHandler implements HttpHandler<HandlerRequest> {

    public async build(req: HandlerRequest): Promise<HTTPOperatorRequest> {
        return this.buildHttpRequest(
            this.createBaseRequest({
                playerCode: req.initRequest.playerCode,
                gameCode: req.initRequest.gameCode,
                token: req.initRequest.token
            }),
            "/supplier/generic/v2/user/authorize",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey
        );
    }

    public parse(response: superagent.Response): Promise<AuthorizeResponse> {
        return this.parseHttpResponse<AuthorizeResponse>(response);
    }
}
