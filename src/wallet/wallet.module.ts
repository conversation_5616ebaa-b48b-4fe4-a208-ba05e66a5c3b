import { <PERSON>du<PERSON> } from "@nestjs/common";
import { StartGameService } from "./start/startGame.service";
import { PaymentModule, StartGameModule } from "@skywind-group/sw-integration-core";
import config from "../config";
import { BalanceService } from "./balance/balance.service";
import { CommitBetService } from "./payment/commitBet.service";
import { CommitWinService } from "./payment/commitWin.service";
import { RollbackService } from "./payment/rollback.service";
import { FinalizationHandler, FinalizationService, FinalizeBetHandler } from "./payment/finalization.service";
import { AuthorizeHandler } from "./start/authorize.handler";

@Module({
    providers: [
        StartGameService,
        BalanceService,
        CommitBetService,
        CommitWinService,
        RollbackService,
        FinalizationHandler,
        FinalizeBetHandler,
        FinalizationService,
        AuthorizeHandler
    ],
    exports: [
        StartGameService,
        FinalizeBetHandler,
        FinalizationHandler,
        AuthorizeHandler,
        BalanceService,
        CommitBetService,
        CommitWinService,
        RollbackService
    ],
    imports: [
        StartGameModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                createGameURL: StartGameService,
                createGameToken: StartGameService,
                loginTerminal: StartGameService
            },
            [WalletModule]
        ),
        PaymentModule.registerWithConfig(
            {
                http: { gatewayConfig: config.http },
                brokenGame: FinalizationService
            },
            [WalletModule]
        )
    ]
})
export class WalletModule {
}
