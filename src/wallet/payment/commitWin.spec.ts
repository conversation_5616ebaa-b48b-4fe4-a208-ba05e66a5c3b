import { privateKey } from "../balance/balance.spec";
import { ResponseStatus } from "../../common/entities/hub88.entities";
import { CommitWinService } from "./commitWin.service";
import { OperatorTransientError } from "@skywind-group/sw-integration-core";
import { BrandFinalizationType, InterruptSocket } from "@skywind-group/sw-wallet-adapter-core";

describe("CommitWin", () => {
    const commitWinService: CommitWinService = new CommitWinService();

    it("build", async () => {
        const request = {
            roundEnded: true,
            roundId: "roundId",
            transactionId: {
                timestamp: 5555555,
                publicId: "publicId",
                serialId: 111
            },
            win: 25
        };
        const gameTokenData = {
            playerCode: "playedCode",
            currency: "USD",
            gameCode: "gameCode",
            merchantType: "merchType",
            brandId: 777777,
            isPromoInternal: false,
            merchantCode: "merchCode",
            token: "token"
        } as any;
        const result = await commitWinService.build({
            request: request as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData
        });
        expect(result.payload.supplier_user).toEqual("playedCode");
        expect(result.payload.game_code).toEqual("gameCode");
        expect(result.payload.amount).toEqual(2500000);
        expect(result.payload.is_free).toEqual(false);
        expect(result.payload.transaction_uuid).toEqual("publicId_win");
        expect(result.payload.reference_transaction_uuid).toEqual("publicId_bet");
        expect(result.payload.round).toEqual("roundId");
        expect(result.payload.currency).toEqual("USD");
        expect(result.payload.round_closed).toEqual(true);
        expect(result.payload.request_uuid.length).not.toEqual(0);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });

    it("build with finalization", async () => {
        const request = {
            roundEnded: true,
            roundId: "roundId",
            transactionId: {
                timestamp: 5555555,
                publicId: "publicId",
                serialId: 111
            },
            win: 25,
            finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
        };
        const gameTokenData = {
            playerCode: "playedCode",
            currency: "USD",
            gameCode: "gameCode",
            merchantType: "merchType",
            brandId: 777777,
            isPromoInternal: false,
            merchantCode: "merchCode",
            token: "token"
        } as any;
        const result = await commitWinService.build({
            request: request as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData
        });
        expect(result.payload.round_closed).toEqual(false);
    });

    it("parse", async () => {
        const result = await commitWinService.parse(
            { body: { balance: 100000, status: ResponseStatus.RS_OK }, status: 200 } as any,
            { gameTokenData: { currency: "USD" }, request: { win: 1 } } as any
        );
        expect(result).toStrictEqual({
            previousValue: 0,
            main: 1
        });
    });

    it("parse with retry", async () => {
        try {
            await commitWinService.parse(
                { body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN }, status: 500 } as any,
                { gameTokenData: { currency: "USD" } } as any
            );
        } catch (err) {
            expect(err).toStrictEqual(new OperatorTransientError(ResponseStatus.RS_ERROR_UNKNOWN, 807));
        }
    });

    it("parse with live error", async () => {
        try {
            await commitWinService.parse(
                { body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN }, status: 500 } as any,
                { gameTokenData: { currency: "USD", isLiveGame: true }, request: { offlineRetry: false } } as any
            );
        } catch (err) {
            expect(err).toStrictEqual(new InterruptSocket());
        }
    });
});
