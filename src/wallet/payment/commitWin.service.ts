import {
    CommitPaymentRequest,
    Http<PERSON><PERSON><PERSON>,
    HTTPOperatorRequest,
    HttpPaymentHandler
} from "@skywind-group/sw-integration-core";
import { BaseHandler } from "../base.handler";
import { CommitRequest, IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import * as superagent from "superagent";
import {
    Balance,
    BrandFinalizationType,
    CannotCompletePayment
} from "@skywind-group/sw-wallet-adapter-core";
import { ResponseStatus, WinRequest } from "../../common/entities/hub88.entities";
import { convertAmountForHub88, convertAmountForSkywind } from "../utils";

@HttpPaymentHandler("commitWinPayment")
@HttpPaymentHandler("commitJackpotWinPayment")
export class CommitWinService extends BaseHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    constructor() {
        super();
    }

    public async build(req: CommitPaymentRequest<CommitRequest>): Promise<HTTPOperatorRequest> {
        const win = convertAmountForHub88(req.request.win);
        const winRequest: WinRequest = {
            ...this.createBaseRequest(req.gameTokenData),
            amount: win,
            is_free: false,
            transaction_uuid: `${req.request.transactionId.publicId}_win`,
            round: `${req.request.roundId}`,
            currency: req.gameTokenData.currency,
            round_closed:
                req.request.finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS
                    ? false
                    : req.request.roundEnded,
            reference_transaction_uuid: `${req.request.transactionId.publicId}_bet`
        };
        return this.buildHttpRequest(
            winRequest,
            "/supplier/generic/v2/transaction/win",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey,
            req.gameTokenData.isLiveGame
        );
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balance> {
        if (response.body.status !== ResponseStatus.RS_OK) {
            this.interruptSocketForLiveGame(req.request, req.gameTokenData);
        }
        if (response.body.status !== ResponseStatus.RS_OK && req.request?.offlineRetry) {
            throw new CannotCompletePayment();
        }

        const result = await this.parseHttpResponse(response, true);
        const balance: Balance = { main: convertAmountForSkywind(result.balance) };
        balance.previousValue = balance.main - req.request.win;
        return balance;
    }
}
