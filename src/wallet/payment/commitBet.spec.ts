import { CommitBetService } from "./commitBet.service";
import { privateKey } from "../balance/balance.spec";
import { RequireRefundBetError } from "../../error";
import { ResponseStatus } from "../../common/entities/hub88.entities";

describe("CommitBet", () => {
    const commitBetService: CommitBetService = new CommitBetService();

    it("build", async () => {
        const request = {
            roundEnded: false,
            roundId: "roundId",
            transactionId: {
                timestamp: 5555555,
                publicId: "publicId",
                serialId: 111
            },
            bet: 25
        };
        const gameTokenData = {
            playerCode: "playedCode",
            currency: "USD",
            gameCode: "gameCode",
            merchantType: "merchType",
            brandId: 777777,
            isPromoInternal: false,
            merchantCode: "merchCode",
            token: "token"
        } as any;
        const result = await commitBetService.build({
            request: request as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData
        });
        expect(result.payload.supplier_user).toEqual("playedCode");
        expect(result.payload.game_code).toEqual("gameCode");
        expect(result.payload.amount).toEqual(2500000);
        expect(result.payload.is_free).toEqual(false);
        expect(result.payload.transaction_uuid).toEqual("publicId_bet");
        expect(result.payload.round).toEqual("roundId");
        expect(result.payload.currency).toEqual("USD");
        expect(result.payload.round_closed).toEqual(false);
        expect(result.payload.request_uuid.length).not.toEqual(0);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });

    it("parse", async () => {
        const result = await commitBetService.parse(
            { body: { balance: 100000, status: ResponseStatus.RS_OK }, status: 200 } as any,
            { gameTokenData: { currency: "USD" }, request: { bet: 1 } } as any
        );
        expect(result).toStrictEqual({
            previousValue: 2,
            main: 1
        });
    });

    it("parse with refund 500", async () => {
        try {
            await commitBetService.parse(
                { body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN }, status: 500 } as any,
                { gameTokenData: { currency: "USD" }, request: { offlineRetry: false } } as any
            );
        } catch (err) {
            expect(err).toStrictEqual(new RequireRefundBetError());
        }
    });

    it("parse without refund 500 during offline retries with zero bet", async () => {
        try {
            await commitBetService.parse(
                { body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN }, status: 500 } as any,
                { gameTokenData: { currency: "USD" }, request: { offlineRetry: true, bet: 0 } } as any
            );
        } catch (err) {
            expect(err.message).toStrictEqual("RS_ERROR_UNKNOWN");
        }
    });

    it("parse with refund 500 during offline retries with non-zero bet", async () => {
        try {
            await commitBetService.parse(
                { body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN }, status: 500 } as any,
                { gameTokenData: { currency: "USD" }, request: { offlineRetry: true, bet: 1 } } as any
            );
        } catch (err) {
            expect(err).toStrictEqual(new RequireRefundBetError());
        }
    });
});
