import {
    BrokenGameRequest, BrokenGameSupport, CommitPaymentRequest,
    HttpGateway,
    HttpHandler,
    HTTPOperatorRequest
} from "@skywind-group/sw-integration-core";
import { <PERSON>Handler } from "../base.handler";
import {
    Balance,
    BrandFinalizationType,
    CannotCompletePayment
} from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { convertAmountForSkywind } from "../utils";
import { BetRequest, ResponseStatus, WinRequest } from "../../common/entities/hub88.entities";
import { Injectable } from "@nestjs/common";
import { BalanceService } from "../balance/balance.service";
import { IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import { logging } from "@skywind-group/sw-utils";
import { CommitWinService } from "./commitWin.service";

const log = logging.logger("finalize-game");

@Injectable()
export class FinalizeBetHandler extends BaseHandler implements HttpHandler<BrokenGameRequest> {
    public async build(req: BrokenGameRequest): Promise<HTTPOperatorRequest> {
        const betRequest: BetRequest = {
            ...this.createBaseRequest(req.gameTokenData),
            amount: 0,
            is_free: false,
            transaction_uuid: `${req.request.transactionId.publicId}_bet`,
            round: `${req.request.roundId}`,
            currency: req.gameTokenData.currency,
            round_closed: false
        };
        return this.buildHttpRequest(
            betRequest,
            "/supplier/generic/v2/transaction/bet",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey
        );
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        if (
            response.body.status !== ResponseStatus.RS_OK &&
            response.body.status !== ResponseStatus.RS_ERROR_DUPLICATE_TRANSACTION
        ) {
            throw new CannotCompletePayment();
        }
        // no need to parse and return balance in case of success
        // it seems that this 0-bet is needed as an 'accompanying' bet for the following 0-win that will close round
        return undefined;
    }
}

@Injectable()
export class FinalizationHandler extends BaseHandler implements HttpHandler<BrokenGameRequest> {
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly finalizeBetHandler: FinalizeBetHandler
    ) {
        super();
    }

    public async build(req: BrokenGameRequest): Promise<HTTPOperatorRequest> {
        await this.httpGateway.request(req, this.finalizeBetHandler);
        const winRequest: WinRequest = {
            ...this.createBaseRequest(req.gameTokenData),
            amount: 0,
            is_free: false,
            transaction_uuid: `${req.request.transactionId.publicId}_win`,
            round: `${req.request.roundId}`,
            currency: req.gameTokenData.currency,
            round_closed: true,
            reference_transaction_uuid: `${req.request.transactionId.publicId}_bet`
        };
        return this.buildHttpRequest(
            winRequest,
            "/supplier/generic/v2/transaction/win",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey
        );
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        // per my understanding - there is no sense to retry finalization if any non-success status was returned
        // but in case of RS_ERROR_UNKNOWN - lets give it a chance to retry
        if (response.body.status !== ResponseStatus.RS_OK && response.body.status !== ResponseStatus.RS_ERROR_UNKNOWN) {
            throw new CannotCompletePayment();
        }
        const result = await this.parseHttpResponse(response, true);
        const balance: Balance = { main: convertAmountForSkywind(result.balance) };
        balance.previousValue = balance.main;
        return balance;
    }
}

@Injectable()
export class FinalizationService implements BrokenGameSupport<IntegrationGameTokenData> {
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly finalizationHandler: FinalizationHandler,
        private readonly balanceHandler: BalanceService,
        private readonly winHandler: CommitWinService
    ) {}

    public async finalizeGame(req: BrokenGameRequest<IntegrationGameTokenData>): Promise<Balance> {
        if (req.request.finalizationType === BrandFinalizationType.OFFLINE_PAYMENTS) {
            try {
                const balances = await this.httpGateway.request(req, this.balanceHandler);
                return {
                    main: balances[req.gameTokenData.currency].main
                };
            } catch (err) {
                log.warn(err, "Failed to get balance during finalization. Returning zero balance.");
                return {
                    main: 0
                };
            }
        }
        return this.httpGateway.request(req, this.finalizationHandler);
    }

    public finalizeWithOfflinePayments(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        return this.httpGateway.request(req, this.winHandler);
    }
}
