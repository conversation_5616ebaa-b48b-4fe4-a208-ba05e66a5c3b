import { privateKey } from "../balance/balance.spec";
import { ResponseStatus } from "../../common/entities/hub88.entities";
import { OperatorTransientError } from "@skywind-group/sw-integration-core";
import { RollbackService } from "./rollback.service";
import { InterruptSocket } from "@skywind-group/sw-wallet-adapter-core";

describe("rollback", () => {
    const rollbackService: RollbackService = new RollbackService();

    it("build", async () => {
        const request = {
            roundEnded: false,
            roundId: "roundId",
            transactionId: {
                timestamp: 5555555,
                publicId: "publicId",
                serialId: 111
            },
            win: 25
        };
        const gameTokenData = {
            playerCode: "playedCode",
            currency: "USD",
            gameCode: "gameCode",
            merchantType: "merchType",
            brandId: 777777,
            isPromoInternal: false,
            merchantCode: "merchCode",
            token: "token"
        } as any;
        const result = await rollbackService.build({
            request: request as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData
        });
        expect(result.payload.supplier_user).toEqual("playedCode");
        expect(result.payload.game_code).toEqual("gameCode");
        expect(result.payload.transaction_uuid).toEqual("publicId");
        expect(result.payload.reference_transaction_uuid).toEqual("publicId_bet");
        expect(result.payload.round).toEqual("roundId");
        expect(result.payload.round_closed).toEqual(false);
        expect(result.payload.request_uuid.length).not.toEqual(0);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });

    it("parse", async () => {
        const result = await rollbackService.parse(
            {
                body: { balance: 100000, status: ResponseStatus.RS_OK },
                status: 200
            } as any,
            { request: {} } as any
        );
        expect(result).toStrictEqual({
            main: 1
        });
    });

    it("parse with retry", async () => {
        try {
            await rollbackService.parse(
                {
                    body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN },
                    status: 500
                } as any,
                { request: {} } as any
            );
        } catch (err) {
            expect(err).toStrictEqual(new OperatorTransientError(ResponseStatus.RS_ERROR_UNKNOWN, 807));
        }
    });

    it("parse with live game error", async () => {
        try {
            await rollbackService.parse(
                {
                    body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN },
                    status: 500
                } as any,
                { gameTokenData: { isLiveGame: true }, request: { offlineRetry: false } } as any
            );
        } catch (err) {
            expect(err).toStrictEqual(new InterruptSocket());
        }
    });
});
