import { privateKey } from "../balance/balance.spec";
import { ResponseStatus } from "../../common/entities/hub88.entities";
import { HttpGateway, OperatorTransientError, BrokenGameRequest } from "@skywind-group/sw-integration-core";
import { FinalizationHandler, FinalizeBetHandler, FinalizationService } from "./finalization.service";
import config from "../../config";
import { BalanceService } from "../balance/balance.service";
import { BrandFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import { IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import { CommitWinService } from "./commitWin.service";

describe("finalization", () => {
    const finalizeBetHandler = new FinalizeBetHandler();
    const httpGateway = new HttpGateway(config.http);
    const finalizationHandler: FinalizationHandler = new FinalizationHandler(
        httpGateway,
        finalizeBetHandler
    );

    it("build", async () => {
        jest.spyOn(httpGateway, "request").mockImplementation(async () => {
            return Promise.resolve();
        });
        const request = {
            roundEnded: false,
            roundId: "roundId",
            transactionId: {
                timestamp: 5555555,
                publicId: "publicId",
                serialId: 111
            },
            roundStatistics: {
                totalWin: 25
            }
        };
        const gameTokenData = {
            playerCode: "playedCode",
            currency: "USD",
            gameCode: "gameCode",
            merchantType: "merchType",
            brandId: 777777,
            isPromoInternal: false,
            merchantCode: "merchCode",
            token: "token"
        } as any;
        const result = await finalizationHandler.build({
            request: request as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData
        });
        expect(result.payload.supplier_user).toEqual("playedCode");
        expect(result.payload.game_code).toEqual("gameCode");
        expect(result.payload.amount).toEqual(0);
        expect(result.payload.is_free).toEqual(false);
        expect(result.payload.transaction_uuid).toEqual("publicId_win");
        expect(result.payload.reference_transaction_uuid).toEqual("publicId_bet");
        expect(result.payload.round).toEqual("roundId");
        expect(result.payload.currency).toEqual("USD");
        expect(result.payload.round_closed).toEqual(true);
        expect(result.payload.request_uuid.length).not.toEqual(0);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });

    it("parse", async () => {
        const result = await finalizationHandler.parse({
            body: { balance: 100000, status: ResponseStatus.RS_OK },
            status: 200
        } as any);
        expect(result).toStrictEqual({
            previousValue: 1,
            main: 1
        });
    });

    it("parse with retry", async () => {
        try {
            await finalizationHandler.parse({
                body: { balance: 1000, status: ResponseStatus.RS_ERROR_UNKNOWN },
                status: 500
            } as any);
        } catch (err) {
            expect(err).toStrictEqual(new OperatorTransientError(ResponseStatus.RS_ERROR_UNKNOWN, 807));
        }
    });

    it("build finalizeBetHandler", async () => {
        const request = {
            roundEnded: false,
            roundId: "roundId",
            transactionId: {
                timestamp: 5555555,
                publicId: "publicId",
                serialId: 111
            },
            roundStatistics: {
                totalWin: 25
            }
        };
        const gameTokenData = {
            playerCode: "playedCode",
            currency: "USD",
            gameCode: "gameCode",
            merchantType: "merchType",
            brandId: 777777,
            isPromoInternal: false,
            merchantCode: "merchCode",
            token: "token"
        } as any;
        const result = await finalizeBetHandler.build({
            request: request as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData
        });
        expect(result.payload.supplier_user).toEqual("playedCode");
        expect(result.payload.game_code).toEqual("gameCode");
        expect(result.payload.amount).toEqual(0);
        expect(result.payload.is_free).toEqual(false);
        expect(result.payload.transaction_uuid).toEqual("publicId_bet");
        expect(result.payload.round).toEqual("roundId");
        expect(result.payload.currency).toEqual("USD");
        expect(result.payload.round_closed).toEqual(false);
        expect(result.payload.request_uuid.length).not.toEqual(0);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });
});

describe("FinalizationService", () => {
    let finalizationService: FinalizationService;
    let httpGateway: HttpGateway;
    let finalizationHandler: FinalizationHandler;
    let balanceService: BalanceService;
    let commitWinService: CommitWinService;

    beforeEach(() => {
        httpGateway = new HttpGateway(config.http);
        const finalizeBetHandler = new FinalizeBetHandler();
        finalizationHandler = new FinalizationHandler(httpGateway, finalizeBetHandler);
        balanceService = {} as BalanceService;
        commitWinService = {} as CommitWinService;
        finalizationService = new FinalizationService(httpGateway, finalizationHandler, balanceService, commitWinService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("finalizeGame", () => {
        const mockGameTokenData: IntegrationGameTokenData = {
            playerCode: "player123",
            currency: "USD",
            gameCode: "game123",
            merchantType: "type1",
            brandId: 123,
            isPromoInternal: false,
            merchantCode: "merchant123",
            token: "token123"
        } as any;

        const mockRequest: BrokenGameRequest<IntegrationGameTokenData> = {
            request: {
                roundId: "round123",
                transactionId: {
                    timestamp: 1234567890,
                    publicId: "public123",
                    serialId: 1
                },
                finalizationType: BrandFinalizationType.ROUND_STATISTICS
            },
            merchantInfo: {
                params: {
                    serverUrl: "https://test.com",
                    privateKey: privateKey
                }
            },
            gameTokenData: mockGameTokenData
        } as any;

        it("should finalize game with regular finalization type", async () => {
            const expectedBalance = { main: 100, previousValue: 100 };
            jest.spyOn(httpGateway, "request").mockResolvedValueOnce(expectedBalance);

            const result = await finalizationService.finalizeGame(mockRequest);

            expect(httpGateway.request).toHaveBeenCalledWith(mockRequest, finalizationHandler);
            expect(result).toEqual(expectedBalance);
        });

        it("should handle offline payments finalization and return balance", async () => {
            const offlineRequest = {
                ...mockRequest,
                request: {
                    ...mockRequest.request,
                    finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
                }
            };

            const mockBalances = {
                USD: { main: 250, bonus: 0 },
                EUR: { main: 200, bonus: 0 }
            };

            jest.spyOn(httpGateway, "request").mockResolvedValueOnce(mockBalances);

            const result = await finalizationService.finalizeGame(offlineRequest);

            expect(httpGateway.request).toHaveBeenCalledWith(offlineRequest, balanceService);
            expect(result).toEqual({ main: 250 });
        });

        it("should return zero balance when offline payments finalization fails", async () => {
            const offlineRequest = {
                ...mockRequest,
                request: {
                    ...mockRequest.request,
                    finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
                }
            };

            jest.spyOn(httpGateway, "request").mockRejectedValueOnce(new Error("Balance service error"));

            const result = await finalizationService.finalizeGame(offlineRequest);

            expect(httpGateway.request).toHaveBeenCalledWith(offlineRequest, balanceService);
            expect(result).toEqual({ main: 0 });
        });

        it("should handle different currencies in offline payments", async () => {
            const eurRequest = {
                ...mockRequest,
                request: {
                    ...mockRequest.request,
                    finalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
                },
                gameTokenData: {
                    ...mockGameTokenData,
                    currency: "EUR"
                }
            };

            const mockBalances = {
                USD: { main: 250, bonus: 0 },
                EUR: { main: 200, bonus: 0 }
            };

            jest.spyOn(httpGateway, "request").mockResolvedValueOnce(mockBalances);

            const result = await finalizationService.finalizeGame(eurRequest);

            expect(httpGateway.request).toHaveBeenCalledWith(eurRequest, balanceService);
            expect(result).toEqual({ main: 200 });
        });
    });
});
