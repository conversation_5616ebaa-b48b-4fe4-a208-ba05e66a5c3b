import {
    <PERSON>tt<PERSON><PERSON><PERSON><PERSON>,
    HTTPOperatorRequest,
    HttpPaymentHandler,
    RefundRequest
} from "@skywind-group/sw-integration-core";
import { <PERSON>Handler } from "../base.handler";
import { CommitRequest, IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import { ResponseStatus, RollbackRequest } from "../../common/entities/hub88.entities";
import { Balance, CannotCompletePayment } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { convertAmountForSkywind } from "../utils";

@HttpPaymentHandler("refundBetPayment")
export class RollbackService extends BaseHandler implements HttpHandler<RefundRequest<IntegrationGameTokenData>> {
    constructor() {
        super();
    }

    public async build(req: RefundRequest<CommitRequest>): Promise<HTTPOperatorRequest> {
        const rollbackRequest: RollbackRequest = {
            ...this.createBaseRequest(req.gameTokenData),
            transaction_uuid: req.request.transactionId.publicId,
            reference_transaction_uuid: `${req.request.transactionId.publicId}_bet`,
            round: `${req.request.roundId}`,
            round_closed: req.request.roundEnded
        };
        return this.buildHttpRequest(
            rollbackRequest,
            "/supplier/generic/v2/transaction/rollback",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey,
            req.gameTokenData.isLiveGame
        );
    }

    public async parse(response: superagent.Response, req: RefundRequest<CommitRequest>): Promise<Balance> {
        if (
            response.body.status === ResponseStatus.RS_ERROR_DUPLICATE_TRANSACTION ||
            response.body.status === ResponseStatus.RS_ERROR_TRANSACTION_DOES_NOT_EXIST
        ) {
            // return fake balance if rollback is already accepted by hub88 or there is nothing to rollback
            return { main: 0 };
        }
        if (response.body.status !== ResponseStatus.RS_OK) {
            this.interruptSocketForLiveGame(req.request, req.gameTokenData);
        }
        if (response.body.status !== ResponseStatus.RS_OK && req.request?.offlineRetry) {
            throw new CannotCompletePayment();
        }
        const result = await this.parseHttpResponse(response, true);
        return { main: convertAmountForSkywind(result.balance) };
    }
}
