import {
    CommitPaymentRequest,
    HttpHand<PERSON>,
    HTTPOperatorRequest,
    HttpPaymentHandler
} from "@skywind-group/sw-integration-core";
import { BaseHandler } from "../base.handler";
import { CommitRequest, IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { OperatorError, RequireRefundBetError } from "../../error";
import { BetRequest, ErrorResponse, ResponseStatus } from "../../common/entities/hub88.entities";
import { convertAmountForHub88, convertAmountForSkywind } from "../utils";

@HttpPaymentHandler("commitBetPayment")
export class CommitBetService extends BaseHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    constructor() {
        super();
    }

    public async build(req: CommitPaymentRequest<CommitRequest>): Promise<HTTPOperatorRequest> {
        const bet = convertAmountForHub88(req.request.bet);
        const betRequest: BetRequest = {
            ...this.createBaseRequest(req.gameTokenData),
            amount: bet,
            is_free: false,
            transaction_uuid: `${req.request.transactionId.publicId}_bet`,
            round: `${req.request.roundId}`,
            currency: req.gameTokenData.currency,
            round_closed: req.request.roundEnded
        };
        return this.buildHttpRequest(
            betRequest,
            "/supplier/generic/v2/transaction/bet",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey,
            req.gameTokenData.isLiveGame
        );
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balance> {
        const isNeedRefund =
            response.body.status !== ResponseStatus.RS_OK &&
            response.body.status !== ResponseStatus.RS_ERROR_NOT_ENOUGH_MONEY;
        if (isNeedRefund && (!req.request.offlineRetry || req.request.bet)) {
            const operatorError = response.body as ErrorResponse;
            this.log.error(new OperatorError(response.status, operatorError.status));
            throw new RequireRefundBetError();
        }
        const result = await this.parseHttpResponse(response);
        const balance: Balance = { main: convertAmountForSkywind(result.balance) };
        balance.previousValue = balance.main + req.request.bet;
        return balance;
    }
}
