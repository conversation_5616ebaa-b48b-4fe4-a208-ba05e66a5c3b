import * as crypto from "crypto";
import { HTTPOperatorRequest, OperatorTransientError } from "@skywind-group/sw-integration-core";
import { logging, sleep } from "@skywind-group/sw-utils";
import { ConnectionError, InterruptSocket, SWError, PaymentRequest } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import config from "../config";
import { BaseRequest, BaseResponse, ErrorResponse, ResponseStatus } from "../common/entities/hub88.entities";
import { AdapterError, OperatorError, UnknowError } from "../error";
import { IntegrationGameTokenData } from "../common/entities/skywind.entities";
import { randomUUID } from "node:crypto";

type OperatorPayload = BaseRequest;

export class BaseHandler {
    protected readonly log = logging.logger("http");

    protected async buildHttpRequest(
        payload: OperatorPayload,
        url: string,
        merchServerUrl: string,
        merchPrivateKey: string,
        shouldRetry?: boolean
    ): Promise<HTTPOperatorRequest> {
        return {
            baseUrl: merchServerUrl,
            uri: url,
            method: "post",
            payload: payload,
            options: {
                headers: {
                    "X-Hub88-Signature": this.getSing(payload, merchPrivateKey)
                }
            },
            retry: !shouldRetry ? this.retry() : this.disabledRetry()
        };
    }

    // need retry for win and rollback
    public async parseHttpResponse<RES extends BaseResponse = BaseResponse>(response: superagent.Response, isNeedRetry?: boolean): Promise<RES> {
        if (response.body.status !== ResponseStatus.RS_OK) {
            const operatorError = response.body as ErrorResponse;
            this.log.error(new OperatorError(response.status, operatorError.status));
            if (isNeedRetry && (operatorError.status === ResponseStatus.RS_ERROR_UNKNOWN || response.status !== 200)) {
                throw new OperatorTransientError(operatorError.status, 807);
            }
            throw this.mapToSWError(response.status, operatorError);
        }
        return response.body;
    }

    protected getSing(payload: OperatorPayload, merchPrivateKey: string): string {
        return crypto
            .createSign(config.operator.cryptoAlgorithm)
            .update(JSON.stringify(payload))
            .sign(merchPrivateKey, "base64");
    }

    public mapToSWError(responseStatus: number, { status }: ErrorResponse): SWError {
        switch (status) {
            case ResponseStatus.RS_ERROR_INVALID_TOKEN:
                return new AdapterError(400, 804, "Game launcher token error");
            case ResponseStatus.RS_ERROR_NOT_ENOUGH_MONEY:
                return new AdapterError(400, 91, "Insufficient balance");
            case ResponseStatus.RS_ERROR_USER_DISABLED:
                return new AdapterError(400, 712, "Player is suspended");
            case ResponseStatus.RS_ERROR_TOKEN_EXPIRED:
                return new AdapterError(400, 323, "Game token expired");
            case ResponseStatus.RS_ERROR_DUPLICATE_TRANSACTION:
                return new AdapterError(208, 33, "Duplicate transaction error");
            case ResponseStatus.RS_ERROR_TRANSACTION_DOES_NOT_EXIST:
                return new AdapterError(404, 600, "Transaction not found");
            default:
                return new UnknowError(status);
        }
    }

    protected createBaseRequest(gameToken: Pick<IntegrationGameTokenData, "playerCode" | "gameCode" | "token">): BaseRequest {
        return {
            supplier_user: gameToken.playerCode,
            game_code: gameToken.gameCode,
            token: gameToken.token,
            request_uuid: randomUUID()
        };
    }

    public retry(): (err) => Promise<boolean> {
        let attempt = config.operator.retryPolicy.attempts;
        return async (err: Error): Promise<boolean> => {
            if ((err instanceof ConnectionError || err instanceof OperatorTransientError) && --attempt > 0) {
                await sleep(config.operator.retryPolicy.sleepInterval);
                return true;
            } else {
                return false;
            }
        };
    }

    /**
     * This method is needed for disabling a retry requests mechanism for live games
     */
    public disabledRetry(): (err) => Promise<boolean> {
        return async (): Promise<boolean> => {
            return false;
        };
    }

    protected interruptSocketForLiveGame(request: PaymentRequest, gameTokenData: IntegrationGameTokenData) {
        if (!request?.offlineRetry && gameTokenData?.isLiveGame) {
            throw new InterruptSocket();
        }
    }
}
