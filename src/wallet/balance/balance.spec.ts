import { BalanceService } from "./balance.service";
import * as superagent from "superagent";
import { CommitPaymentRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import { ResponseStatus } from "../../common/entities/hub88.entities";
import { UnknowError } from "../../error";

export const privateKey = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

describe("Balance", () => {
    const balanceService = new BalanceService();

    it("Build", async () => {
        const result = await balanceService.build({
            request: {
                transactionId: {
                    publicId: "publicId"
                }
            } as any,
            merchantInfo: { params: { serverUrl: "", privateKey: privateKey } } as any,
            gameTokenData: {
                playerCode: "123",
                currency: "USD",
                gameCode: "gameCode",
                merchantType: "123",
                brandId: 123,
                isPromoInternal: false,
                merchantCode: "123",
                token: "token"
            } as any
        });
        expect(result.payload.supplier_user).toEqual("123");
        expect(result.payload.game_code).toEqual("gameCode");
        expect(result.payload.request_uuid.length).not.toEqual(0);
        expect(result.payload.token).toEqual("token");
        expect(result.options.headers).toHaveProperty("X-Hub88-Signature");
        expect(result.method).toBe("post");
    });

    it("Parse", async () => {
        expect(
            await balanceService.parse(
                { body: { balance: 654123, status: ResponseStatus.RS_OK }, status: 200 } as superagent.Response,
                { gameTokenData: { currency: "USD" } } as CommitPaymentRequest<IntegrationGameTokenData>
            )
        ).toStrictEqual({
            USD: { main: 6.54 }
        });
    });

    it("Parse with error", async () => {
        try {
            await balanceService.parse(
                {
                    body: { balance: 654123, status: ResponseStatus.RS_ERROR_UNKNOWN },
                    status: 200
                } as superagent.Response,
                { gameTokenData: { currency: "USD" } } as CommitPaymentRequest<IntegrationGameTokenData>
            );
        } catch (e) {
            expect(e).toStrictEqual(new UnknowError(ResponseStatus.RS_ERROR_UNKNOWN));
        }
    });
});
