import {
    CommitPaymentRequest,
    <PERSON>ttp<PERSON><PERSON><PERSON>,
    HTTPOperatorRequest,
    HttpPaymentHandler
} from "@skywind-group/sw-integration-core";
import { BaseHandler } from "../base.handler";
import { IntegrationGameTokenData } from "../../common/entities/skywind.entities";
import { Balances } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { convertAmountForSkywind } from "../utils";
import { BaseRequest } from "../../common/entities/hub88.entities";

@HttpPaymentHandler("getBalances")
export class BalanceService extends BaseHandler implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>> {
    public async build(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<HTTPOperatorRequest> {
        const balanceRequest: BaseRequest = {
            ...this.createBaseRequest(req.gameTokenData)
        };
        return this.buildHttpRequest(
            balanceRequest,
            "/supplier/generic/v2/user/balance",
            req.merchantInfo.params.serverUrl,
            req.merchantInfo.params.privateKey
        );
    }

    public async parse(
        response: superagent.Response,
        req: CommitPaymentRequest<IntegrationGameTokenData>
    ): Promise<Balances> {
        const result = await this.parseHttpResponse(response);
        return {
            [req.gameTokenData.currency]: { main: convertAmountForSkywind(result.balance) }
        };
    }
}
