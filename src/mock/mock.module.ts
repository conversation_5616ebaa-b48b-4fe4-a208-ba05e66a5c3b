import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { MerchantGuard } from "./merchant.guard";
import { CustomerGuard } from "./customer.guard";
import { <PERSON>ck<PERSON>ontroller } from "./mock.controller";
import { PaymentResponder } from "./responders/payment.responder";
import { MockService } from "./mock.service";

@Module({
    controllers: [MockController],
    providers: [MerchantGuard, CustomerGuard, MockService],
    imports: [mock.MockModule]
})
export class AdapterMockModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        consumer
            .apply(PaymentResponder)
            .forRoutes(
                "/supplier/generic/v2/user/balance",
                "/supplier/generic/v2/transaction/bet",
                "/supplier/generic/v2/transaction/win",
                "/supplier/generic/v2/transaction/rollback"
            );
    }
}
