import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import {
    BaseRequest,
    BaseResponse,
    BetRequest,
    ResponseStatus,
    RollbackRequest
} from "../common/entities/hub88.entities";

@Injectable()
export class MockService extends mock.MockService {
    constructor(
        protected readonly customerService: mock.CustomerService,
        protected readonly sessionService: mock.SessionService,
        protected readonly ticketService: mock.TicketService,
        protected readonly transactionService: mock.TransactionService
    ) {
        super(customerService, sessionService, ticketService, transactionService);
    }

    protected getAmount(body: BetRequest): number {
        return body.amount;
    }

    protected getAuthResponse(merchant: mock.Merchant, customer: mock.Customer): any {
        return {
            PlayerId: customer.cust_id,
            Token: customer.cust_session_id,
            Balance: customer.balance.amount,
            CurrencyCode: customer.balance.currency_code,
            CountryCode: customer.country,
            OperatorCode: merchant.merch_id,
            ErrorCode: 0
        };
    }

    public getBalance(merchant: mock.Merchant, customer: mock.Customer, body: BaseRequest): BaseResponse {
        return {
            user: customer.cust_id,
            status: ResponseStatus.RS_OK,
            request_uuid: body.request_uuid,
            currency: customer.currency_code,
            balance: customer.balance.amount
        };
    }

    protected getDebitTransactionIdInRollbackRequest(body: RollbackRequest): string {
        return body.reference_transaction_uuid;
    }

    protected getPaymentResponse(
        merchant: mock.Merchant,
        customer: mock.Customer,
        transaction: mock.Transaction,
        body: BaseRequest
    ): BaseResponse {
        return {
            user: customer.cust_id,
            status: ResponseStatus.RS_OK,
            request_uuid: body.request_uuid,
            currency: customer.currency_code,
            balance: customer.balance.amount
        };
    }

    protected getTicketId(body: BaseRequest): string {
        return body.token;
    }

    protected getTransactionId(body: BetRequest): string {
        return body.transaction_uuid;
    }

    protected isFreebet(body: any, action: mock.WalletAction): boolean {
        return !!(action === mock.WalletAction.Debit && body.BonusId);
    }
}
