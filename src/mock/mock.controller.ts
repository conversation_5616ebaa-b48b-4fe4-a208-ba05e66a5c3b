import { Controller, Post, UseFilters, UseGuards, Body, UseInterceptors } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { CustomerGuard } from "./customer.guard";
import { ErrorsFilter } from "./errors.filter";
import { MockService } from "./mock.service";
import { BaseRequest, BetRequest, RollbackRequest, WinRequest } from "../common/entities/hub88.entities";

@Controller("/supplier/generic/v2")
@UseGuards(CustomerGuard)
@UseInterceptors(mock.CustomErrorInterceptor, mock.ExtraDataInterceptor)
@UseFilters(ErrorsFilter)
export class MockController {
    constructor(private readonly service: MockService) {}

    @Post("/user/balance")
    public balance(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: BaseRequest
    ) {
        return this.service.getBalance(merchant, customer, body);
    }

    @Post("/transaction/bet")
    public debit(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: BetRequest
    ) {
        return this.service.debit(merchant, customer, body);
    }

    @Post("/transaction/win")
    public credit(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: WinRequest
    ) {
        return this.service.credit(merchant, customer, body);
    }

    @Post("/transaction/rollback")
    public rollback(
        @mock.MerchantParam() merchant: mock.Merchant,
        @mock.CustomerParam() customer: mock.Customer,
        @Body() body: RollbackRequest
    ) {
        return this.service.rollback(merchant, customer, body);
    }
}
