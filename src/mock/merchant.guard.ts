import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Injectable()
export class MerchantGuard extends mock.MerchantGuard {
    constructor(protected merchantService: mock.MerchantService) {
        super(merchantService);
    }
    getMerchantCode(req: any): string {
        return req.body?.Account?.Username;
    }

    throwError(): void {
        throw new Error("Merchant guard error");
    }
}
