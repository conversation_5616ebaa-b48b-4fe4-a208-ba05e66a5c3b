import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { BaseRequest, BaseResponse, ResponseStatus } from "../../common/entities/hub88.entities";

@Injectable()
export class PaymentResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService, private ticketService: mock.TicketService) {
        super(settingsService);
    }

    public async getResponse(req: any, body: BaseRequest): Promise<BaseResponse> {
        const [, currency] = this.ticketService.getDataFromTicket(body.token);
        return {
            user: body.supplier_user,
            status: ResponseStatus.RS_OK,
            request_uuid: body.request_uuid,
            currency,
            balance: this.settingsService.settings.amount
        };
    }
}
