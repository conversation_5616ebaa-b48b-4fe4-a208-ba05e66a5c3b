import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Injectable()
export class CustomerGuard implements CanActivate {
    constructor(private ticketService: mock.TicketService, private merchantService: mock.MerchantService) {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const sessionId = request.body.token;
        const { merchantId, customerId } = this.ticketService.getById(sessionId);

        const merchant = this.merchantService.getById(merchantId);
        const customer = this.merchantService.getCustomerById(merchantId, customerId);

        if (!merchant || !customer) {
            throw new Error("Merchant or customer is not defined!");
        }

        request.merchant = merchant;
        request.customer = customer;
        return true;
    }
}
