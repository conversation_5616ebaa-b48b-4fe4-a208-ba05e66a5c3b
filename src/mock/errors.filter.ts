import { mock } from "@skywind-group/sw-integration-core";
import { Catch, HttpException, HttpStatus } from "@nestjs/common";
import { TransactionNotFound } from "@skywind-group/sw-integration-core/lib/mock/errors";
import { ResponseStatus } from "../common/entities/hub88.entities";
import { logging } from "@skywind-group/sw-utils";

@Catch()
export class ErrorsFilter extends mock.ExceptionsFilter {
    private readonly log = logging.logger("mock-error-filter");

    private TRX_NOT_FOUND_ERROR_CODE = -7;

    public getStatusAndBody(error: any): [number, any] {
        this.log.error("Error on mock", error);
        if (error instanceof TransactionNotFound || error.error_code  === this.TRX_NOT_FOUND_ERROR_CODE) {
            return [200, { status: ResponseStatus.RS_ERROR_TRANSACTION_DOES_NOT_EXIST }];
        }

        const status = error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

        const body = {
            ErrorCode: 900,
            Message: "Hub88 internal error"
        };

        return [status, body];
    }
}
