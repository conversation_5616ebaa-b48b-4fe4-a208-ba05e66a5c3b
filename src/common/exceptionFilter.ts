import { ArgumentsHost, BadRequestException, Catch, ExceptionFilter, HttpException, HttpStatus } from "@nestjs/common";
import type { FastifyReply } from "fastify";

@Catch()
export class BaseErrorsFilter<T> implements ExceptionFilter<T> {
    public catch(error: any, host: ArgumentsHost): void {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<FastifyReply<any>>();
        const status =
            error instanceof HttpException
                ? error.getStatus()
                : error.responseStatus || HttpStatus.INTERNAL_SERVER_ERROR;
        switch (true) {
            case error instanceof BadRequestException:
                response.status(status).send({
                    statusCode: status,
                    code: (error as any).code || undefined,
                    message: (error as any).response.message
                });
            default:
                response.status(status).send({
                    statusCode: status,
                    code: error.code || undefined,
                    message: error.message
                });
        }
    }
}
