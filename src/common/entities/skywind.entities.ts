import {
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantInfo,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { Platform } from "./hub88.entities";

export interface IntegrationMerchantGameInitRequest extends MerchantGameInitRequest {
    country: string;
    playerCode?: string;
    currency: string;
    platform: Platform;
    token?: string;
    meta?: any;
    subPartnerId?: string;
}

export interface IntegrationMerchantGameURLInfo {
    urlParams: any;
    tokenData: IntegrationStartGameTokenData;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData {
    platform: string;
    token?: string;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData {
    token?: string;
}

export interface MerchantGameHistoryRequest {
    merchantType: string;
    merchantCode: string;
    roundId: string;
    finishDate: string;
    ttl?: number;
    showRoundInfo: boolean;
}

export interface MerchantLobbyLaunchRequest {
    merchantType: string;
    merchantCode: string;
    language: string;
    ticket: string;
}

export interface MerchantGameHistoryResponse {
    imageUrl: string;
    ttl?: number;
}

export type CommitRequest = IntegrationGameTokenData & MerchantInfo;
