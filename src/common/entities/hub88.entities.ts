import { IsDefined, IsIn, <PERSON>NotEmpty, IsOptional, ValidateIf } from "class-validator";

export interface LaunchObject {
    url: string;
}

export interface SWGameLaunchResponse {
    url: string;
    token: string;
}

export enum Platform {
    DESKTOP = "GPL_DESKTOP",
    MOBILE = "GPL_MOBILE"
}

export class GameListRequest {
    @IsDefined()
    @IsNotEmpty()
    operator_id: string;

    @IsOptional()
    @IsNotEmpty()
        // optional param for testing - we need to be able to pick right merchant type with this param
    merchantType?: string;
}

export class HistoryRequest {
    @IsDefined()
    @IsNotEmpty()
    operator_id: string;

    @IsDefined()
    @IsNotEmpty()
    round: string;

    @IsOptional()
    @IsNotEmpty()
    transaction_uuid?: string;

    @IsOptional()
    @IsNotEmpty()
    user?: string;

    @IsOptional()
    @IsNotEmpty()
        // optional param for testing - we need to be able to pick right merchant type with this param
    merchantType?: string;
}

export class GameUrlRequest {
    @IsDefined()
    @IsNotEmpty()
    currency: string;

    @ValidateIf((req) => req.currency !== "XXX")
    @IsDefined()
    @IsNotEmpty()
    user: string;

    @ValidateIf((req) => req.currency !== "XXX")
    @IsDefined()
    @IsNotEmpty()
    token: string;

    @IsOptional()
    @IsNotEmpty()
    sub_partner_id?: string;

    @IsDefined()
    @IsNotEmpty()
    @IsIn(["GPL_DESKTOP", "GPL_MOBILE"])
    platform: Platform;

    @IsDefined()
    @IsNotEmpty()
    operator_id: string;

    @IsOptional()
    @IsNotEmpty()
    meta?: any;

    @IsDefined()
    @IsNotEmpty()
    lobby_url: string;

    @IsDefined()
    @IsNotEmpty()
    lang: string;

    @IsDefined()
    @IsNotEmpty()
    ip: string;

    @IsDefined()
    @IsNotEmpty()
    game_code: string;

    @IsOptional()
    @IsNotEmpty()
    deposit_url?: string;

    @IsDefined()
    @IsNotEmpty()
    country: string;

    @IsOptional()
    @IsNotEmpty()
        // optional param for testing - we need to be able to pick right merchant type with this param
    merchantType?: string;
}

export enum ResponseStatus {
    RS_OK = "RS_OK",
    RS_ERROR_UNKNOWN = "RS_ERROR_UNKNOWN",
    RS_ERROR_INVALID_PARTNER = "RS_ERROR_INVALID_PARTNER",
    RS_ERROR_INVALID_TOKEN = "RS_ERROR_INVALID_TOKEN",
    RS_ERROR_INVALID_GAMERS_ERROR_WRONG_CURRENCY = "RS_ERROR_INVALID_GAMERS_ERROR_WRONG_CURRENCY",
    RS_ERROR_NOT_ENOUGH_MONEY = "RS_ERROR_NOT_ENOUGH_MONEY",
    RS_ERROR_USER_DISABLED = "RS_ERROR_USER_DISABLED",
    RS_ERROR_INVALID_SIGNATURE = "RS_ERROR_INVALID_SIGNATURE",
    RS_ERROR_TOKEN_EXPIRED = "RS_ERROR_TOKEN_EXPIRED",
    RS_ERROR_WRONG_SYNTAX = "RS_ERROR_WRONG_SYNTAX",
    RS_ERROR_WRONG_TYPES = "RS_ERROR_WRONG_TYPES",
    RS_ERROR_DUPLICATE_TRANSACTION = "RS_ERROR_DUPLICATE_TRANSACTION",
    RS_ERROR_TRANSACTION_DOES_NOT_EXIST = "RS_ERROR_TRANSACTION_DOES_NOT_EXIST",
    RS_ERROR_OPERATOR_API = "RS_ERROR_OPERATOR_API"
}

export interface ErrorResponse {
    status: ResponseStatus;
}

export interface BaseResponse {
    user: string;
    status: ResponseStatus;
    request_uuid: string;
    currency: string;
    balance: number;
}

export interface AuthorizeResponse extends BaseResponse {
    token: string;
}

export interface BaseRequest {
    supplier_user?: string;
    token: string;
    game_code: string;
    request_uuid: string;
}

export interface BetRequest extends BaseRequest {
    transaction_uuid: string;
    round_closed: boolean;
    round: string;
    reward_uuid?: string;
    is_free: boolean;
    game_code: string;
    currency: string;
    bet?: any;
    amount: number;
}

export interface WinRequest extends BetRequest {
    reference_transaction_uuid: string;
}

export interface RollbackRequest extends BaseRequest {
    transaction_uuid: string;
    round_closed: boolean;
    round: string;
    game_code: string;
    reference_transaction_uuid: string;
}

export interface GameListResponse {
    url_thumb: string;
    url_background: string;
    product: string;
    platforms: Array<Platform>;
    name: string;
    freebet_support?: boolean;
    game_code: string;
    enabled: boolean;
    category: string;
    blocked_countries: Array<string>;
}

export enum Hub88GameTypes {
    Unknown = "Unknown",
    Baccarat = "Baccarat",
    Bet_On_Poker = "Bet On Poker",
    Blackjack = "Blackjack",
    Casual_Games = "Casual Games",
    Jackpot_Slots = "Jackpot Slots",
    Live_Baccarat = "Live Baccarat",
    Live_Blackjack = "Live Blackjack",
    Live_Dealer = "Live Dealer",
    Live_Dice = "Live Dice",
    Live_Games = "Live Games",
    Live_Keno = "Live Keno",
    Live_Lottery = "Live Lottery",
    Live_Roulette = "Live Roulette",
    Lottery = "Lottery",
    Multiplayer = "Multiplayer",
    Player_Props = "Player Props",
    Poker = "Poker",
    Roulette = "Roulette",
    Scratch_Cards = "Scratch Cards",
    Sportsbook = "Sportsbook",
    Table_Games = "Table Games",
    Video_Poker = "Video Poker",
    Video_Slots = "Video Slots",
    Virtual_Sports = "Virtual Sports",
    Wheel_of_Fortune = "Wheel of Fortune",
    Bingo = "Bingo",
    Fixed_Odds = "Fixed Odds"
}
