module.exports = {
    ignorePatterns: [
        "lib/*",
        "lib/**/*",
        ".husky/*",
        ".husky/**/*",
        ".eslintrc.js",
        "eslint.config.js",
        "node_modules/*",
        "node_modules/**/*",
    ],
    parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: [
            require.resolve("./tsconfig.json")
        ]
    },
    parser: '@typescript-eslint/parser',
    plugins: ['@typescript-eslint/eslint-plugin'],
    extends: [
        "plugin:@typescript-eslint/eslint-recommended",
        "plugin:@typescript-eslint/recommended",
        "prettier"
    ],
    root: true,
    env: {
        node: true,
        jest: true,
    },
    rules: {
        '@typescript-eslint/interface-name-prefix': 'off',
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-use-before-define': 'off',
        '@typescript-eslint/no-empty-function': 'off',
        '@typescript-eslint/no-non-null-assertion': 'off',
        'quotes': ["error", "double"],
        'semi': 'off',
        '@typescript-eslint/semi': ["error"],
        "@typescript-eslint/member-delimiter-style": ["error"],
        'no-console': ["error"],
        "@typescript-eslint/no-unused-vars": "off",
        "object-curly-spacing": ["warn", "always"],
        "semi-style": ["warn", "last"],
        "no-extra-semi": ["warn"],
        "semi-spacing": ["warn", { "before": false, "after": true }],
        "comma-dangle": ["error", {
            "arrays": "never",
            "objects": "never",
            "imports": "never",
            "exports": "never",
            "functions": "never"
        }]
    },
};
