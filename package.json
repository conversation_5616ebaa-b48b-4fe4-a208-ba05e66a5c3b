{"name": "sw-integration-hub88", "version": "5.55.0", "description": "", "private": true, "license": "UNLICENSED", "scripts": {"clean": "rm -rf ./lib", "build": "pnpm run version && nest build  ", "format": "prettier --write \"**/*.ts\"", "build:dev": "nest build --watch", "start:launcher": "nest start launcher", "start:launcher:dev": "MEASURES_PROVIDER=prometheus nest start launcher --watch", "start:launcher:debug": "MEASURES_PROVIDER=prometheus nest start launcher --debug --watch", "start:operator": "nest start operator", "start:operator:dev": "MEASURES_PROVIDER=prometheus nest start operator --watch", "start:operator:debug": "MEASURES_PROVIDER=prometheus nest start operator --debug --watch", "start:wallet": "nest start wallet", "start:wallet:dev": "MEASURES_PROVIDER=prometheus nest start wallet --watch", "start:wallet:debug": "MEASURES_PROVIDER=prometheus nest start wallet --debug --watch", "start:mock": "nest start mock", "start:mock:dev": "MEASURES_PROVIDER=prometheus nest start mock --watch", "start:mock:debug": "MEASURES_PROVIDER=prometheus nest start mock --debug --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "MEASURES_BASE_INSTRUMENT=false jest", "test:coverage": "pnpm run test --coverage", "version": "mkdir -p lib && echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/version", "preinstall": "node preinstall.cjs"}, "dependencies": {"@fastify/static": "7.0.2", "@nestjs/common": "10.4.5", "@nestjs/core": "10.4.5", "@nestjs/platform-fastify": "10.4.5", "@skywind-group/sw-integration-core": "2.0.11", "@skywind-group/sw-utils": "2.0.64", "@skywind-group/sw-wallet-adapter-core": "2.0.31", "@skywind-group/gelf-stream": "1.2.6", "agentkeepalive": "4.5.0", "bole": "5.0.15", "bole-console": "0.1.10", "class-transformer": "0.5.1", "class-validator": "0.14.1", "emitter-listener": "1.1.2", "express-prom-bundle": "7.0.2", "kafka-node": "5.0.0", "prom-client": "15.0.0", "reflect-metadata": "0.2.2"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "10.4.17", "@types/jest": "^29.5.14", "@types/node": "^20.17.46", "@types/superagent": "^8.1.9", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "~2.31.0", "eslint-plugin-prettier": "^5.4.0", "fastify": "4.29.1", "husky": "^8.0.3", "jest": "^29.7.0", "jsonwebtoken": "^9.0.2", "lint-staged": "15.5.2", "prettier": "3.5.3", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.8.3"}, "packageManager": "pnpm@9.15.4", "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.service.ts"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}