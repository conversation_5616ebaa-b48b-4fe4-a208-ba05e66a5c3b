FROM node:20.16.0-alpine as build

WORKDIR /app
COPY . /app/

RUN apk add --no-cache git && apk cache clean

RUN corepack enable && \
    corepack prepare pnpm@9.15.4 --activate && \
    pnpm install --no-frozen-lockfile && \
    pnpm run lint && \
    pnpm run clean && \
    pnpm run build

FROM node:20.16.0-alpine

EXPOSE 6000

WORKDIR /app

COPY --chown=node:node --from=build /app/package.json /app/pnpm-lock.yaml /app/preinstall.cjs /app/.npmrc ./
COPY --chown=node:node --from=build /app/lib ./lib

RUN corepack enable && \
    corepack prepare pnpm@9.15.4 --activate && \
    pnpm install --frozen-lockfile --prod && \
    pnpm store prune && \
    rm .npmrc && \
    rm -fr /root/.cache && \
    corepack disable

USER node
CMD ["node", "/app/lib/mainWallet"]
