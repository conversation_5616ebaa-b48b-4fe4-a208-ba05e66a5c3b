{
  "compilerOptions": {
    "target": "es2022",
    "lib": ["es2022"],
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "declaration": true,
    "noImplicitAny": false,
    "removeComments": true,
    "noLib": false,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "outDir": "./lib",
    "rootDir": "./src",
    "skipLibCheck": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules"
  ]
}
